import router from '../router'
import Tool from '@/common/tool'
import DialogManager from './dialogManager'
import webViewBackHandler from './webviewBackHandler'

const whiteList = ['/personalPrivacy','/instructionManual','/externalLink','/externalLink/gallery','/init','/login','/uLinkerInstructionManual','/uLinker_consultation'] //路由白名单

// 确保webview返回处理器已初始化
webViewBackHandler.init();
router.beforeEach(async(to, from, next) => {
    console.log('routeInterception to:', to);
    console.log('routeInterception from:', from);
    const isInit = Tool.isInit()
    if(to.path.startsWith('/init')){
        next()
        return
    }
    if(!isInit){
        if(whiteList.includes(to.path)){
            const fullPath = to.fullPath || to.path
            next(`/init?previousPath=${encodeURIComponent(fullPath)}`)
        }else{
            next(`/init`)
        }

        return
    }
    for(let i = 0;i<whiteList.length;i++){
        if(to.path.indexOf(whiteList[i])>-1){
            next()
            return
        }
    }

    // 会诊模式路由拦截：如果目标路由是 /index，检查会诊模式
    if (to.path === '/index') {
        const consultationMode = Tool.getConsultationMode();
        if (consultationMode === 1) {
            // 如果是会诊模式，重定向到会诊页面
            next('/uLinker_consultation');
            return;
        }
        // 普通模式，继续后续逻辑
    }

    // 使用DialogManager处理路由变化时的弹窗逻辑
    const toMatchedLength = to.matched.length;
    const fromMatchedLength = from.matched.length;

    if (toMatchedLength > fromMatchedLength) {
        console.log('前进');
    } else if (toMatchedLength < fromMatchedLength) {
        console.log('回退');
        // 使用DialogManager处理回退时的弹窗
        if (DialogManager.hasOpenDialogs()) {
            // 委托给DialogManager处理路由变化
            DialogManager.handleRouteChange(to, from, next);
            return;
        }
    } else {
        console.log('替换或同级跳转');
        if (!whiteList.includes(to.path) && !whiteList.includes(from.path)) {
            // 使用DialogManager处理同级跳转时的弹窗
            if (DialogManager.hasOpenDialogs()) {
                // 委托给DialogManager处理路由变化
                DialogManager.handleRouteChange(to, from, next);
                return;
            }
        }
    }
    const loginToken=Tool.getToken()

    if(!loginToken){
        next('/login')
    }else{
        next()
    }
})

// 路由后置守卫：用于跨路由嵌套层级的跳转
router.afterEach(()=>{
    if (window.directPath) {
        router.replace(window.directPath);
        window.directPath=''
    }
})

