
import Tool from '@/common/tool'
export default {
    data(){
        return {
            currentDialogId:0
        }
    },
    watch: {
        dialogVisible: {
            handler(val) {
                this.dialogVisibleWatch&&this.dialogVisibleWatch(val)
                if(val){
                    this.currentDialogId = Tool.genID(3)
                    this.$root.currentDialogList.push({
                        id:this.currentDialogId,
                        el:this
                    })
                }else{
                    this.$root.currentDialogList = this.$root.currentDialogList.filter(item=>item.id!==this.currentDialogId)
                }
            }
        },
    },
    computed:{

    },
    methods:{
        checkCanCloseOnPopstate(){
            return true
        },
        closeDialog() {
            this.dialogVisible = false;
        },
        checkCanClose(){
            return true
        },
        checkIsShow(){
            return this.dialogVisible
        },
        showDialog(){
            this.dialogVisible = true;
        },
    }
}
